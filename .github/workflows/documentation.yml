name: Documentation
on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"

permissions:
  contents: write

jobs:
  Build-Documentation:
    runs-on: [nvidia-a100]
    timeout-minutes: 30

    steps:
      - name: Checkout branch
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Clear docs
        run: |
          rm -rf /tmp/triton-docs
        continue-on-error: true

      - name: Install dependent packages
        run: |
          sudo pip3 install tabulate cmake sphinx matplotlib myst_parser sphinx-rtd-theme pandas pytest sphinx-gallery sphinx-multiversion llnl-hatchet

      #- name: Fetch dependent branches
      #  run: |
      #    git fetch origin main:main

      - name: Build docs
        run: |
          cd docs
          export PATH=$(python3 -c "import cmake; print(cmake.CMAKE_BIN_DIR)"):$PATH
          # Limit the number of threads to reduce CPU memory usage
          # This CI node has 24 cores
          MAX_JOBS=24 sudo -E python3 -m sphinx . _build/html/main

      - name: Update docs
        run: |
          sudo mkdir /tmp/triton-docs/
          sudo mv docs/_build/html/* /tmp/triton-docs/
          sudo git checkout gh-pages
          sudo cp -r CNAME /tmp/triton-docs/
          sudo cp -r index.html /tmp/triton-docs/
          sudo cp -r .nojekyll /tmp/triton-docs/
          sudo rm -rf *
          sudo cp -r /tmp/triton-docs/* .
          sudo git add .
          sudo git config --global user.email "N/A"
          sudo git config --global user.name "gh-actions-bot"
          sudo git commit -am "[GH-PAGES] Updated website"

      - name: Publish docs
        run: |
          sudo git push origin gh-pages
