# These owners will be the default owners for everything in
# the repo. Unless a later match takes precedence,
# @global-owner1 and @global-owner2 will be requested for
# review when someone opens a pull request.
*       @ptillet

# --------
# Analyses
# --------
# Alias analysis
include/triton/Analysis/Alias.h @Jokeren
lib/Analysis/Alias.cpp @Jokeren
# Allocation analysis
include/triton/Analysis/Allocation.h @Jokeren
lib/Analysis/Allocation.cpp @Jokeren
# Membar analysis
include/triton/Analysis/Membar.h @Jokeren
lib/Analysis/Membar.cpp @Jokeren
# AxisInfo analysis
include/triton/Analysis/AxisInfo.h @ptillet
lib/Analysis/AxisInfo.cpp @ptillet
# Utilities
include/triton/Analysis/Utility.h @Jokeren
lib/Analysis/Utility.cpp @Jokeren

# ----------
# Dialects
# ----------
# Pipeline pass
lib/Dialect/TritonGPU/Transforms/Pipeline.cpp @ptillet
# Prefetch pass
lib/Dialect/TritonGPU/Transforms/Prefetch.cpp @ptillet
# Coalesce pass
lib/Dialect/TritonGPU/Transforms/Coalesce.cpp @ptillet
# Layout simplification pass
lib/Dialect/TritonGPU/Transforms/Combine.cpp @ptillet

# -----------
# Conversions
# -----------
# TritonToTritonGPU
include/triton/Conversion/TritonToTritonGPU/ @ptillet
lib/Dialect/TritonGPU/Transforms/TritonGPUConversion.cpp @ptillet

# -----------
# third_party
# -----------
third_party/amd/ @antiagainst @zhanglx13
third_party/proton/ @Jokeren @crobeck @fywkevin

# -----------
# gluon
# -----------
python/triton/experimental/gluon/ @peterbell10
python/src/gluon_ir.cc @peterbell10
python/test/gluon @peterbell10
test/Gluon @peterbell10
include/triton/Dialect/Gluon @peterbell10
lib/Dialect/Gluon @peterbell10

# -----------
# Linear Layouts
# -----------
lib/Tools/ @lezcano
lib/Dialect/TritonGPU/IR/LinearLayoutConversions.cpp @lezcano
