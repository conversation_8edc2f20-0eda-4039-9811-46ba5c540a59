name: Report a bug
description: Report triton failing to compile a kernel, or giving incorrect results
labels: ["bug"]

body:
- type: markdown
  attributes:
    value: |
      #### Disclaimer
      The core triton team is small and has very limited capacity. We may not have time to look into your report.
      For the best results, please:
        - Avoid submitting duplicates. Search through [the existing and past issues](https://github.com/triton-lang/triton/issues?q=is%3Aissue+sort%3Acreated-desc+) first to see if it's been reported previously.
        - Check if the issue persists with a build from the latest source.
        - Provide all relevant information in the initial report, to prevent unnecessary back and forth discussion.
        - If you can, try to diagnose and/or fix the issue yourself. We welcome high quality contributions.
- type: textarea
  attributes:
    label: Describe the bug
    description: |
      Please provide a clear and concise description of what the bug is.

      If relevant, add a [minimal complete example](https://stackoverflow.com/help/minimal-reproducible-example) that reproduces the bug. It is very important for the snippet to be as simple as possible, so please take time to trim down any irrelevant code to help us debug efficiently. We are going to copy-paste your code and we expect to get the same result as you did, so include both the kernel and launching code as well as any relevant imports.

      If the code is too long (hopefully, it isn't), feel free to put it in a public gist and link it in the issue: https://gist.github.com.

      Please also paste or describe the results you observe instead of the expected results. If you observe an error, please paste the error message including the **full** traceback of the exception. It may be relevant to wrap error messages in ```` ```triple quotes blocks``` ````.
    placeholder: |
      A clear and concise description of what the bug is.

      ```python
      # Sample code to reproduce the problem
      ```

      ```
      The error message you got, with the full traceback.
      ```
  validations:
    required: true
- type: textarea
  attributes:
    label: Environment details
    description: |
      Please include any relevant context about how you're running the reproducer e.g. which version of triton, and what GPU you are using.
    placeholder: |
        Triton: ...
        GPU: ...
  validations:
    required: true
