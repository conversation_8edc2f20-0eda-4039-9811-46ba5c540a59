### How to run a Triton Community Meetup

Contributors:  <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>

Last updated: July 3, 2025

Community meetups give the on-line community a chance to interact with each other and the Triton developers in a more face-to-face format vs slack chats.  Example topics covered during community meetups include:
* Developers presenting updates on features they’re working on.
* Developers asking community for feedback on new initiatives
* Questions from community for developers
* Questions about Triton strategy/direction.


## Some logistics

Community meetups occur once 8 weeks (usually during the first 1-2 weeks of a month).
Reminders are sent out 2 weeks ahead of time

Only companies that paid for corp Microsoft Teams access can create webinars.  Three folks who have done this (or have access in the past are):
* Are<PERSON>
* Whitney Tsang
* Ksharma <PERSON>war
* Jian Hui

Webinars are automatically recorded.  The person with corp access can upload the video to youtube after the webinar is finished.

Only the person with corp access can open a webinar.  Even if you’re a registered speaker or MC, you’ll see

<div align="center">
  <img src="https://private-user-images.githubusercontent.com/85795580/459649517-42acf22a-6ae3-4c0f-a585-dffc25fabc33.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE1NTg1MzEsIm5iZiI6MTc1MTU1ODIzMSwicGF0aCI6Ii84NTc5NTU4MC80NTk2NDk1MTctNDJhY2YyMmEtNmFlMy00YzBmLWE1ODUtZGZmYzI1ZmFiYzMzLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTA3MDMlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNzAzVDE1NTcxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWZhZWQ2NjQ3MDIxNGVkNThkMGYxZmNjMmI2Yzg5MjMyZTAxZGI5NmFiZDIwYzczMDc0N2YzOTM3MTYwMThiOWEmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.UwzTbqMIsFw1Ex9t-5HY8A3KHzOnY6eqgqz2eJ0qTbE" alt="Microsoft Meeting waiting for meeting to start view">
</div>


before the meeting owner opens it up.

During the meetup, take notes.

Post the final notes on the Triton-lang website here: https://github.com/triton-lang/triton/tree/main/docs/meetups

## How to run a community meetup

1. Work with one of the folks above to create a Microsoft Teams webinar (occurring 6-8 weeks in the future).  Template:

<pre>
Title: “Triton Community Meetup (online)”
External presenter: **“<your name>”**
Co-organizer: **add organizers**
    Date: **Add date**
    Time: 10:00-11:00 PDT
    Duration: 1 hr
    Recurring meeting: link **(<NAME_EMAIL>)**
</pre>

2. If you don’t have details about the meeting (e.g. meeting ID, passcode, phone number, etc.) you can login to the meeting, click on More -> Meeting Info and get data that way.

<div align="center">
  <img src="https://private-user-images.githubusercontent.com/85795580/459649518-f9a924df-2020-4dde-b8bb-e901a9298cef.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE1NTg1MzEsIm5iZiI6MTc1MTU1ODIzMSwicGF0aCI6Ii84NTc5NTU4MC80NTk2NDk1MTgtZjlhOTI0ZGYtMjAyMC00ZGRlLWI4YmItZTkwMWE5Mjk4Y2VmLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTA3MDMlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNzAzVDE1NTcxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTM0YzI3OGRmMWI1NDk2NjQxOTJhMmJiNTM5ZDg4ZGZjOTk4OGM2YWRkNTk1ZTY4NjNhMjk2ZWZlZDhiZDYzZGEmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.6bgthh7pHDV-ooMMm1WsgbbLJH39nzk-DPre6GDjZnI" alt="Microsoft Meeting info pane">
</div>

3. Copy the invite generated from the meeting to [triton #general chat](https://app.slack.com/huddle/T01379XQ9FG/C013E22BPPC) on slack. Example text to use in slack:

<pre>
The next community meetup will be on <date> from 10am-11am PST. The meeting link is below. If anyone has agenda items to add for the meetup please reach out to me.

Thanks,
**<your name>**
----
Microsoft Teams Need help?
Join the meeting now <- **change this**
Meeting ID: 247 993 489 59 <- **change this**
Passcode: Dg8TLJ <- **change this**
Dial in by phone
******-849-4874,,637393478# United States, Los Angeles <- **change this**
Find a local number
Phone conference ID: 637 393 478# <- **change this**
</pre>

4. Post the same invite to the [#triton channel on Discord GPU_MODE](https://discord.com/channels/1189498204333543425/1189607595451895918). You will need to join GPU_MODE to post to it.

5. 1-2 Days before the meeting. Verify that someone with corp Microsoft Teams access will open the meeting up for you.
6. Day before meeting, post reminders to slack and discord (reply to your original message):
Reminder, this month's community meetup is tomorrow at 10am PST.

<pre>
Agenda:
   Topic #1 <who>
   Topic #2 <who>
</pre>

7. Day of meeting, login a little early and verify everything is working as expected.

8. During the meeting, keep an eye on the comments section. Some folks might post questions for the speaker there and/or issues they're having with Teams.

9. After the meeting has finished, work with the person with corp Microsoft Teams access to upload the recorded video to youtube.  Post the youtube link in [triton #general chat](https://app.slack.com/huddle/T01379XQ9FG/C013E22BPPC).

If this is your first time using Microsoft Teams, work with the meeting creator to test out the UI (e.g. logging in, verifying your camera, audio work, verifying you can present your screen if using that functionality, play around with hand raising, play around with people/attendees/muting others, log off and log back in again.)

## How to upload videos to Youtube

1. Register to use youtube. That is, the top right symbol when you go to youtube.com should be your login.

<div align="center">
  <img src="https://private-user-images.githubusercontent.com/85795580/459649516-4bea87d7-1c6b-4331-92c1-e480b2f3d295.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE1NTg1MzEsIm5iZiI6MTc1MTU1ODIzMSwicGF0aCI6Ii84NTc5NTU4MC80NTk2NDk1MTYtNGJlYTg3ZDctMWM2Yi00MzMxLTkyYzEtZTQ4MGIyZjNkMjk1LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTA3MDMlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNzAzVDE1NTcxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWVjNWUxMTIzNTA4MGQxMmRkMjQ1NWQzMzFhMTIwNmVhZDNhMmJlMWQwMDQwYmUyNmQ4YTkwNTUxMTI3ZWJjNjAmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.RUIK1GbqVtC8sfu_2ntb8to9FFQv7MfHrYbvNCfLCh8" alt="Youtube Login">
</div>

2. Click on https://www.youtube.com/feed/you
3. Click on “+ Create” on top next to search box.
4. Select the video you want to upload
5. For Title use something like “Triton Community Meetup May 2025”
6. No, it’s not made for kids
7. No video elements
8. Save or publish: “public”
9. Make a copy of the video link so you can post it on slack and discord. (like: https://youtu.be/kJjBurkPn_8)


## Past community meetups

 | Date | Meet setup | Agenda & who | Recording |
 | ---- | ---------- | ------------ | --------- |
 | 2025-05-01 | [Link](https://tinyurl.com/mr397f6x) | Topic: what are plans for existing block pointer programming model? (Context: Intel GPU backend relies heavily on it and will need time to fully move to tensor descriptor programming model.) - Jianhui Li, Intel <br/> Topic: infrastructure for Triton performance tests - Sayce, Google<br/>Topic: what talks/tutorials/open discussions would you like to see at the 2025 Triton Developers’ Summit? How can we help? - Adnan Aziz, Meta <br/> Topic: what are plans for existing block pointer programming model? (Context: Intel GPU backend relies heavily on it and will need time to fully move to tensor descriptor programming model.) - Jianhui Li, Intel<br/>Topic: infrastructure for Triton performance tests - Sayce, Google<br/>Topic: what talks/tutorials/open discussions would you like to see at the 2025 Triton Developers’ Summit? How can we help? - Adnan Aziz, Meta </pre> | https://www.youtube.com/watch?v=W16BrXc5BYE |
| 2025-07-09 |[Link](https://tinyurl.com/mus5wyax) | Topic: Gluon update - Jeff Niu, OpenAI <br/> Topic: Interest and requirements for a nightly performance regression suite - Simon Waters,  kernelize.ai<br/>Triton developer's summit update - Ofer Dekel, Microsoft | |
